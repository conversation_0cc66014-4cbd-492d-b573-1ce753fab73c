import { useState } from 'react';
import { Geolocation } from '@capacitor/geolocation';
import { Storage } from '@ionic/storage';

const storage = new Storage();
storage.create();

export function useOfflineGeolocation() {
  const [coords, setCoords] = useState<{ lat: number; lng: number } | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Ambil lokasi, simpan ke storage
  const getLocation = async () => {
    setLoading(true);
    setError(null);
    try {
      const perm = await Geolocation.checkPermissions();
      if (perm.location !== 'granted') {
        const req = await Geolocation.requestPermissions();
        if (req.location !== 'granted') throw new Error('Izin lokasi ditolak');
      }
      const pos = await Geolocation.getCurrentPosition({ enableHighAccuracy: true });
      const lat = pos.coords.latitude;
      const lng = pos.coords.longitude;
      setCoords({ lat, lng });
      // Simpan ke storage (bisa juga ke SQLite)
      const data = { lat, lng, timestamp: Date.now() };
      await storage.set('last_gps', data);
      // Simpan ke antrian sync jika offline
      if (!navigator.onLine) {
        let queue = (await storage.get('gps_queue')) || [];
        queue.push(data);
        await storage.set('gps_queue', queue);
      }
      return data;
    } catch (e: any) {
      setError(e.message || 'Gagal mendapatkan lokasi');
      throw e;
    } finally {
      setLoading(false);
    }
  };

  // Sync data ke server jika online
  const syncData = async () => {
    if (!navigator.onLine) return;
    const queue = (await storage.get('gps_queue')) || [];
    for (const item of queue) {
      // TODO: Ganti URL dan payload sesuai kebutuhan server Anda
      try {
        await fetch('https://your-server.com/api/sync_gps', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(item),
        });
      } catch (e) {
        // Jika gagal, biarkan di queue
        continue;
      }
    }
    await storage.set('gps_queue', []);
  };

  return { coords, loading, error, getLocation, syncData };
} 