export async function fetchAndStoreJamKerja() {
  try {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const bidangId = user.bidang_id;
    if (!bidangId) return;
    const res = await fetch('https://absensiku.trunois.my.id/api/jam_kerja.php?api_key=absensiku_api_key_2023');
    const data = await res.json();
    if (data.status === 'success' && Array.isArray(data.data)) {
      const jamKerja = data.data.filter((j: any) => j.bidang_id == bidangId);
      localStorage.setItem('jam_kerja_list', JSON.stringify(jamKerja));
    }
  } catch (err) {
    // Optional: handle error (misal: log)
  }
} 