/* HistoriIzinDinas.css */

.histori-izin-dinas-page {
  --background: #f5f5f5;
}

.histori-izin-dinas-header {
  background: linear-gradient(135deg, #1a65eb 0%, #1a65eb 100%);
  color: white;
  padding: 20px 0;
}

.histori-izin-dinas-content {
  padding: 16px;
}

.histori-izin-dinas-card {
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.histori-izin-dinas-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.histori-izin-dinas-quota-card {
  border-left: 4px solid #1a65eb;
  margin-bottom: 20px;
}

.histori-izin-dinas-quota-card.limit-reached {
  border-left-color: #f44336;
}

.histori-izin-dinas-quota-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.histori-izin-dinas-quota-icon {
  font-size: 1.5rem;
  margin-right: 12px;
}

.histori-izin-dinas-quota-title {
  font-weight: bold;
  font-size: 1.1rem;
  color: #333;
}

.histori-izin-dinas-quota-subtitle {
  font-size: 0.9rem;
  color: #666;
}

.histori-izin-dinas-quota-count {
  font-size: 1.5rem;
  font-weight: bold;
  text-align: right;
}

.histori-izin-dinas-quota-count.available {
  color: #4caf50;
}

.histori-izin-dinas-quota-count.limit-reached {
  color: #f44336;
}

.histori-izin-dinas-warning-box {
  margin-top: 12px;
  padding: 8px;
  background-color: #ffebee;
  border-radius: 4px;
  font-size: 0.85rem;
  color: #d32f2f;
}

.histori-izin-dinas-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.histori-izin-dinas-item-date {
  font-size: 0.9rem;
  color: #666;
  display: flex;
  align-items: center;
}

.histori-izin-dinas-item-status {
  display: flex;
  align-items: center;
}

.histori-izin-dinas-periode {
  margin-bottom: 8px;
  font-size: 0.95rem;
}

.histori-izin-dinas-periode-badge {
  margin-left: 8px;
  font-size: 0.8rem;
  color: #666;
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.histori-izin-dinas-tujuan {
  margin-bottom: 8px;
  font-size: 0.95rem;
}

.histori-izin-dinas-keterangan {
  margin-bottom: 12px;
  font-size: 0.95rem;
  line-height: 1.4;
}

.histori-izin-dinas-approval {
  font-size: 0.8rem;
  color: #666;
  border-top: 1px solid #eee;
  padding-top: 8px;
}

.histori-izin-dinas-loading {
  text-align: center;
  padding: 50px;
}

.histori-izin-dinas-loading ion-spinner {
  margin-bottom: 16px;
}

.histori-izin-dinas-error {
  text-align: center;
  padding: 20px;
  color: #f44336;
}

.histori-izin-dinas-error ion-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.histori-izin-dinas-empty {
  text-align: center;
  padding: 50px;
  color: #666;
}

.histori-izin-dinas-empty ion-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.histori-izin-dinas-fab {
  --background: #1a65eb;
  --background-activated: #1557d1;
  --color: white;
  --box-shadow: 0 4px 16px rgba(26, 101, 235, 0.3);
}

.histori-izin-dinas-fab.disabled {
  --background: #9e9e9e;
  --background-activated: #757575;
  --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Status colors */
.histori-izin-dinas-status-approved {
  color: #4caf50;
}

.histori-izin-dinas-status-pending {
  color: #ff9800;
}

.histori-izin-dinas-status-rejected {
  color: #f44336;
}

/* Responsive design */
@media (max-width: 768px) {
  .histori-izin-dinas-content {
    padding: 12px;
  }
  
  .histori-izin-dinas-item-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .histori-izin-dinas-quota-header {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }
  
  .histori-izin-dinas-quota-count {
    text-align: left;
    margin-top: 8px;
  }
}

/* Animation */
.histori-izin-dinas-card {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Badge styling */
.histori-izin-dinas-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.histori-izin-dinas-badge.success {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.histori-izin-dinas-badge.warning {
  background-color: #fff3e0;
  color: #f57c00;
}

.histori-izin-dinas-badge.danger {
  background-color: #ffebee;
  color: #d32f2f;
}

/* Floating action button states */
.histori-izin-dinas-fab-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.histori-izin-dinas-fab-button {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.histori-izin-dinas-fab-button:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.histori-izin-dinas-fab-button.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.histori-izin-dinas-fab-button.disabled:hover {
  transform: none;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* Card hover effects */
.histori-izin-dinas-card-interactive {
  cursor: pointer;
  transition: all 0.2s ease;
}

.histori-izin-dinas-card-interactive:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.histori-izin-dinas-card-interactive:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
