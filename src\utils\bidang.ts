export async function fetchAndStoreBidang() {
  try {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const bidangId = user.bidang_id;
    if (!bidangId) return;
    const res = await fetch('https://absensiku.trunois.my.id/api/bidang.php?api_key=absensiku_api_key_2023');
    const data = await res.json();
    if (data.status === 'success' && Array.isArray(data.data)) {
      const bidang = data.data.filter((b: any) => b.id == bidangId);
      localStorage.setItem('bidang_list', JSON.stringify(bidang));
    }
  } catch (err) {
    // Optional: handle error (misal: log)
  }
} 