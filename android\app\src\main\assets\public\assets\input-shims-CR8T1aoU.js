import{d as A,K as O,e as P,g as B,b as p,f as F,h as H,j as b,k as $,l as U,m as Y}from"./index-CV7ojgvy.js";/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const D=new WeakMap,I=(e,s,t,o=0,r=!1)=>{D.has(e)!==t&&(t?G(e,s,o,r):W(e,s))},q=e=>e===e.getRootNode().activeElement,G=(e,s,t,o=!1)=>{const r=s.parentNode,n=s.cloneNode(!1);n.classList.add("cloned-input"),n.tabIndex=-1,o&&(n.disabled=!0),r.appendChild(n),D.set(e,n);const a=e.ownerDocument.dir==="rtl"?9999:-9999;e.style.pointerEvents="none",s.style.transform="translate3d(".concat(a,"px,").concat(t,"px,0) scale(0)")},W=(e,s)=>{const t=D.get(e);t&&(D.delete(e),t.remove()),e.style.pointerEvents="",s.style.transform=""},N=50,j=(e,s,t)=>{if(!t||!s)return()=>{};const o=a=>{q(s)&&I(e,s,a)},r=()=>I(e,s,!1),n=()=>o(!0),c=()=>o(!1);return P(t,"ionScrollStart",n),P(t,"ionScrollEnd",c),s.addEventListener("blur",r),()=>{B(t,"ionScrollStart",n),B(t,"ionScrollEnd",c),s.removeEventListener("blur",r)}},T="input, textarea, [no-blur], [contenteditable]",z=()=>{let e=!0,s=!1;const t=document,o=()=>{s=!0},r=()=>{e=!0},n=c=>{if(s){s=!1;return}const a=t.activeElement;if(!a||a.matches(T))return;const f=c.target;f!==a&&(f.matches(T)||f.closest(T)||(e=!1,setTimeout(()=>{e||a.blur()},50)))};return P(t,"ionScrollStart",o),t.addEventListener("focusin",r,!0),t.addEventListener("touchend",n,!1),()=>{B(t,"ionScrollStart",o,!0),t.removeEventListener("focusin",r,!0),t.removeEventListener("touchend",n,!1)}},J=.3,Q=(e,s,t,o)=>{var r;const n=(r=e.closest("ion-item,[ion-item]"))!==null&&r!==void 0?r:e;return V(n.getBoundingClientRect(),s.getBoundingClientRect(),t,o)},V=(e,s,t,o)=>{const r=e.top,n=e.bottom,c=s.top,a=Math.min(s.bottom,o-t),f=c+15,u=a-N-n,d=f-r,S=Math.round(u<0?-u:d>0?-d:0),v=Math.min(S,r-c),i=Math.abs(v)/J,h=Math.min(400,Math.max(150,i));return{scrollAmount:v,scrollDuration:h,scrollPadding:t,inputSafeY:-(r-f)+4}},R="$ionPaddingTimer",M=(e,s,t)=>{const o=e[R];o&&clearTimeout(o),s>0?e.style.setProperty("--keyboard-offset","".concat(s,"px")):e[R]=setTimeout(()=>{e.style.setProperty("--keyboard-offset","0px"),t&&t()},120)},_=(e,s,t)=>{const o=()=>{s&&M(s,0,t)};e.addEventListener("focusout",o,{once:!0})};let g=0;const K="data-ionic-skip-scroll-assist",X=(e,s,t,o,r,n,c,a=!1)=>{const f=n&&(c===void 0||c.mode===H.None);let l=!1;const u=b!==void 0?b.innerHeight:0,d=y=>{if(l===!1){l=!0;return}k(e,s,t,o,y.detail.keyboardHeight,f,a,u,!1)},S=()=>{l=!1,b===null||b===void 0||b.removeEventListener("ionKeyboardDidShow",d),e.removeEventListener("focusout",S)},v=async()=>{if(s.hasAttribute(K)){s.removeAttribute(K);return}k(e,s,t,o,r,f,a,u),b===null||b===void 0||b.addEventListener("ionKeyboardDidShow",d),e.addEventListener("focusout",S)};return e.addEventListener("focusin",v),()=>{e.removeEventListener("focusin",v),b===null||b===void 0||b.removeEventListener("ionKeyboardDidShow",d),e.removeEventListener("focusout",S)}},C=e=>{var s;if(document.activeElement===e)return;const t=e.getAttribute("id"),o=e.closest('label[for="'.concat(t,'"]')),r=(s=document.activeElement)===null||s===void 0?void 0:s.closest('label[for="'.concat(t,'"]'));o!==null&&o===r||(e.setAttribute(K,"true"),e.focus())},k=async(e,s,t,o,r,n,c=!1,a=0,f=!0)=>{if(!t&&!o)return;const l=Q(e,t||o,r,a);if(t&&Math.abs(l.scrollAmount)<4){C(s),n&&t!==null&&(M(t,g),_(s,t,()=>g=0));return}if(I(e,s,!0,l.inputSafeY,c),C(s),$(()=>e.click()),n&&t&&(g=l.scrollPadding,M(t,g)),typeof window<"u"){let u;const d=async()=>{u!==void 0&&clearTimeout(u),window.removeEventListener("ionKeyboardDidShow",S),window.removeEventListener("ionKeyboardDidShow",d),t&&await Y(t,0,l.scrollAmount,l.scrollDuration),I(e,s,!1,l.inputSafeY),C(s),n&&_(s,t,()=>g=0)},S=()=>{window.removeEventListener("ionKeyboardDidShow",S),window.addEventListener("ionKeyboardDidShow",d)};if(t){const v=await U(t),y=v.scrollHeight-v.clientHeight;if(f&&l.scrollAmount>y-v.scrollTop){s.type==="password"?(l.scrollAmount+=N,window.addEventListener("ionKeyboardDidShow",S)):window.addEventListener("ionKeyboardDidShow",d),u=setTimeout(d,1e3);return}}d()}},Z=!0,te=async(e,s)=>{if(A===void 0)return;const t=s==="ios",o=s==="android",r=e.getNumber("keyboardHeight",290),n=e.getBoolean("scrollAssist",!0),c=e.getBoolean("hideCaretOnScroll",t),a=e.getBoolean("inputBlurring",!1),f=e.getBoolean("scrollPadding",!0),l=Array.from(A.querySelectorAll("ion-input, ion-textarea")),u=new WeakMap,d=new WeakMap,S=await O.getResizeMode(),v=async i=>{await new Promise(w=>p(i,w));const h=i.shadowRoot||i,m=h.querySelector("input")||h.querySelector("textarea"),L=F(i),x=L?null:i.closest("ion-footer");if(!m)return;if(L&&c&&!u.has(i)){const w=j(i,m,L);u.set(i,w)}if(!(m.type==="date"||m.type==="datetime-local")&&(L||x)&&n&&!d.has(i)){const w=X(i,m,L,x,r,f,S,o);d.set(i,w)}},y=i=>{if(c){const h=u.get(i);h&&h(),u.delete(i)}if(n){const h=d.get(i);h&&h(),d.delete(i)}};a&&Z&&z();for(const i of l)v(i);A.addEventListener("ionInputDidLoad",i=>{v(i.detail)}),A.addEventListener("ionInputDidUnload",i=>{y(i.detail)})};export{te as startInputShims};
