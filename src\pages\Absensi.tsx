import React, { useRef, useState, useEffect } from 'react';
import { IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonButton, IonIcon, IonText, IonButtons, IonBackButton, useIonToast, IonLoading } from '@ionic/react';
import { cameraOutline, checkmarkCircleOutline, refreshOutline, locationOutline, chevronDownOutline, chevronUpOutline } from 'ionicons/icons';
import { useOfflineGeolocation } from '../hooks/useOfflineGeolocation';

const headerStyle: React.CSSProperties = {
  background: 'linear-gradient(135deg, #1880ff 60%, #005be7 100%)',
  boxShadow: '0 4px 18px rgba(24, 128, 255, 0)',
  borderBottomLeftRadius: 32,
  borderBottomRightRadius: 32,
  minHeight: 80,
  padding: '0 0 8px 0',
};
const titleStyle: React.CSSProperties = {
  fontSize: '1.5rem',
  fontWeight: 700,
  letterSpacing: 0.5,
  color: '#fff',
  textShadow: '0 2px 8px rgba(24,128,255,0.13)',
};

function getTodayName(): string {
  const days = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'];
  return days[new Date().getDay()];
}

function isInRadius(lat: number, lng: number, lokasi: any) {
  const toRad = (v: number) => (v * Math.PI) / 180;
  const R = 6371e3; // meter
  const φ1 = toRad(lat);
  const φ2 = toRad(Number(lokasi.latitude));
  const Δφ = toRad(Number(lokasi.latitude) - lat);
  const Δλ = toRad(Number(lokasi.longitude) - lng);
  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) *
    Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const d = R * c;
  return d <= Number(lokasi.radius);
}



const Absensi: React.FC = () => {
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const [photo, setPhoto] = useState<string | null>(null);
  const [error, setError] = useState('');
  const [jamKerjaValid, setJamKerjaValid] = useState(false);
  const [jamKerjaInfo, setJamKerjaInfo] = useState('');
  const [lokasiStatus, setLokasiStatus] = useState<string>('');
  const [present] = useIonToast();
  const { getLocation } = useOfflineGeolocation();
  const [lokasiValid, setLokasiValid] = useState(false);
  const [showRefresh, setShowRefresh] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [offlineCount, setOfflineCount] = useState(0);
  const [currentTime, setCurrentTime] = useState('');
  const [showDetails, setShowDetails] = useState(false);


  // Ambil lokasi kantor dari localStorage
  const lokasiList = JSON.parse(localStorage.getItem('lokasi_list') || '[]');
  const lokasiKantor = lokasiList[0];

  // Fungsi untuk update jumlah data offline
  const updateOfflineCount = () => {
    try {
      const offlineQueue = JSON.parse(localStorage.getItem('offline_absensi_queue') || '[]');
      // Karena sekarang kita langsung hapus data yang berhasil, semua data di queue adalah unsynced
      setOfflineCount(offlineQueue.length);
    } catch (error) {
      console.error('Error updating offline count:', error);
    }
  };

  // Fungsi untuk membersihkan data offline yang sudah lama
  const cleanOldOfflineData = () => {
    try {
      const offlineQueue = JSON.parse(localStorage.getItem('offline_absensi_queue') || '[]');
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      const filteredQueue = offlineQueue.filter((item: any) => {
        const itemDate = new Date(item.timestamp);
        return itemDate > sevenDaysAgo; // Simpan hanya data 7 hari terakhir
      });

      if (filteredQueue.length < offlineQueue.length) {
        localStorage.setItem('offline_absensi_queue', JSON.stringify(filteredQueue));
        console.log(`Cleaned ${offlineQueue.length - filteredQueue.length} old offline records`);
        updateOfflineCount(); // Update counter setelah cleanup
      }
    } catch (error) {
      console.error('Error cleaning old offline data:', error);
    }
  };

  // Fungsi untuk menyimpan data absensi ke localStorage (offline storage)
  const saveAbsensiToLocalStorage = (absensiData: any) => {
    try {
      const existingData = JSON.parse(localStorage.getItem('offline_absensi_queue') || '[]');
      const newData = {
        ...absensiData,
        id: Date.now().toString() + '_' + Math.random().toString(36).substring(2, 11), // ID unik untuk offline
        timestamp: new Date().toISOString()
      };
      existingData.push(newData);
      localStorage.setItem('offline_absensi_queue', JSON.stringify(existingData));
      updateOfflineCount(); // Update counter
      return newData;
    } catch (error) {
      console.error('Error saving to localStorage:', error);
      return null;
    }
  };

  // Fungsi untuk sinkronisasi data offline ke server
  const syncOfflineData = async () => {
    try {
      let offlineQueue = JSON.parse(localStorage.getItem('offline_absensi_queue') || '[]');
      const initialCount = offlineQueue.length;

      if (initialCount === 0) return;

      console.log(`Syncing ${initialCount} offline records...`);
      let successCount = 0;

      // Proses setiap data offline
      for (let i = offlineQueue.length - 1; i >= 0; i--) {
        const data = offlineQueue[i];
        try {
          let response;
          const payload = { ...data };
          delete payload.id; // Hapus ID sementara
          delete payload.timestamp;
          delete payload.synced;
          delete payload.jenisAbsensi; // Hapus field tambahan

          if (data.jenisAbsensi === 'masuk') {
            // POST untuk absensi masuk
            response = await fetch('https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(payload)
            });
          } else {
            // PUT untuk absensi pulang - cari ID record dulu
            const today = data.tanggal;
            const checkResponse = await fetch(`https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023&user_id=${data.user_id}&tanggal=${today}`);
            const checkResult = await checkResponse.json();

            if (checkResult.status === 'success' && checkResult.data.length > 0) {
              payload.id = checkResult.data[0].id;
              response = await fetch('https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023', {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
              });
            } else {
              console.error('Record not found for offline pulang data');
              continue;
            }
          }

          const result = await response.json();
          if (result.status === 'success') {
            // Langsung hapus data yang berhasil dikirim dari array
            offlineQueue.splice(i, 1);
            successCount++;
            console.log('Successfully synced and removed offline record:', data.id);

            // Simpan backup data yang berhasil dikirim
            const backupData = { ...data, synced: true, syncedAt: new Date().toISOString() };
            const existingBackup = JSON.parse(localStorage.getItem('absensi_backup') || '[]');
            existingBackup.push(backupData);
            // Simpan maksimal 30 hari terakhir
            if (existingBackup.length > 30) {
              existingBackup.splice(0, existingBackup.length - 30);
            }
            localStorage.setItem('absensi_backup', JSON.stringify(existingBackup));
          } else {
            console.error('Failed to sync record:', data.id, result.message);
          }
        } catch (error) {
          console.error('Error syncing individual record:', data.id, error);
        }
      }

      // Update localStorage dengan queue yang sudah dibersihkan
      localStorage.setItem('offline_absensi_queue', JSON.stringify(offlineQueue));

      if (successCount > 0) {
        present({
          message: `${successCount} data offline berhasil disinkronkan`,
          color: 'success',
          duration: 3000,
          position: 'top'
        });
      }

      updateOfflineCount(); // Update counter setelah sync
    } catch (error) {
      console.error('Error during sync:', error);
    }
  };



  // State untuk mencegah pengambilan lokasi berulang
  const [lokasiChecked, setLokasiChecked] = useState(false);
  const [jenisAbsensi, setJenisAbsensi] = useState<'masuk' | 'pulang'>('masuk');

  // Monitor status online/offline
  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      console.log('Device is online, syncing offline data...');
      syncOfflineData();
    };

    const handleOffline = () => {
      setIsOnline(false);
      console.log('Device is offline');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Sync saat pertama kali load jika online
    if (navigator.onLine) {
      syncOfflineData();
    }

    // Update offline count saat pertama kali load
    updateOfflineCount();

    // Bersihkan data offline yang sudah lebih dari 7 hari
    cleanOldOfflineData();

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
    // eslint-disable-next-line
  }, []);

  // Cek apakah sudah absen masuk hari ini
  useEffect(() => {
    const checkAbsensiHariIni = async () => {
      try {
        const today = new Date().toISOString().split('T')[0];
        const response = await fetch(`https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023&user_id=${user.id || user.nik}&tanggal=${today}`);
        const result = await response.json();

        if (result.status === 'success' && result.data.length > 0) {
          const absensiHariIni = result.data[0];
          // Jika sudah ada jam_masuk tapi belum ada jam_pulang, maka ini absensi pulang
          if (absensiHariIni.jam_masuk && !absensiHariIni.jam_pulang) {
            setJenisAbsensi('pulang');
          }
        }
      } catch (error) {
        console.log('Error checking absensi hari ini:', error);
        // Jika error, default ke absensi masuk
      }
    };

    if (user.id || user.nik) {
      checkAbsensiHariIni();
    }
  }, [user]);

  useEffect(() => {
    // Ambil lokasi GPS hanya sekali saat halaman dibuka
    const ambilLokasi = async () => {
      if (lokasiChecked || !lokasiKantor) return; // Jangan ambil lokasi jika sudah pernah dicek atau belum ada data lokasi kantor

      setLokasiStatus('Mengecek lokasi...');
      try {
        const data = await getLocation();
        if (!isInRadius(data.lat, data.lng, lokasiKantor)) {
          setLokasiStatus('Anda di luar radius lokasi!');
          setLokasiValid(false);
          setShowRefresh(true);
          present({ message: 'Anda di luar radius lokasi!', color: 'danger', duration: 2000, position: 'top' });
        } else {
          setLokasiStatus('Lokasi valid, dalam radius.');
          setLokasiValid(true);
          setShowRefresh(false);
          present({ message: 'Lokasi valid, dalam radius.', color: 'success', duration: 2000, position: 'top' });
        }
        setLokasiChecked(true); // Tandai bahwa lokasi sudah dicek
      } catch (e) {
        setLokasiStatus('Gagal mendapatkan lokasi');
        setLokasiValid(false);
        setShowRefresh(true);
        present({ message: 'Gagal mendapatkan lokasi', color: 'danger', duration: 2000, position: 'top' });
        setLokasiChecked(true); // Tetap tandai sudah dicek meskipun gagal
      }
    };

    // Hanya jalankan jika ada data lokasi kantor dan belum pernah dicek
    if (lokasiKantor && !lokasiChecked) {
      ambilLokasi();
    }
    // eslint-disable-next-line
  }, [lokasiKantor, lokasiChecked]);

  // Fungsi refresh lokasi manual
  const handleRefreshLokasi = async () => {
    setShowRefresh(false);
    setLokasiStatus('Mengecek ulang lokasi...');
    setLokasiChecked(false); // Reset status checked agar bisa dicek ulang

    try {
      const data = await getLocation();
      if (lokasiKantor && !isInRadius(data.lat, data.lng, lokasiKantor)) {
        setLokasiStatus('Anda di luar radius lokasi!');
        setLokasiValid(false);
        setShowRefresh(true);
        present({ message: 'Anda di luar radius lokasi!', color: 'danger', duration: 2000, position: 'top' });
      } else {
        setLokasiStatus('Lokasi valid, dalam radius.');
        setLokasiValid(true);
        setShowRefresh(false);
        present({ message: 'Lokasi valid, dalam radius.', color: 'success', duration: 2000, position: 'top' });
      }
      setLokasiChecked(true); // Tandai sudah dicek ulang
    } catch (e) {
      setLokasiStatus('Gagal mendapatkan lokasi');
      setLokasiValid(false);
      setShowRefresh(true);
      present({ message: 'Gagal mendapatkan lokasi', color: 'danger', duration: 2000, position: 'top' });
      setLokasiChecked(true); // Tetap tandai sudah dicek meskipun gagal
    }
  };

  // Kode untuk update marker sudah dihapus karena tidak menggunakan peta lagi

  const startCamera = async () => {
    setError('');
    setPhoto(null);
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: { facingMode: 'user' } });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (err) {
      setError('Tidak dapat mengakses kamera. Pastikan izin kamera sudah diberikan.');
    }
  };

  const stopCamera = () => {
    if (videoRef.current && videoRef.current.srcObject) {
      const tracks = (videoRef.current.srcObject as MediaStream).getTracks();
      tracks.forEach(track => track.stop());
      videoRef.current.srcObject = null;
    }
  };

  const takePhoto = () => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
        setPhoto(canvas.toDataURL('image/jpeg'));
      }
      stopCamera();
    }
  };

  const retakePhoto = () => {
    setPhoto(null);
    startCamera();
  };

  // Fungsi untuk validasi jam kerja berdasarkan jenis absensi
  const validateJamKerja = (jenisAbsensi: 'masuk' | 'pulang') => {
    const jamKerjaList = JSON.parse(localStorage.getItem('jam_kerja_list') || '[]');
    const jamKerjaBidangList = JSON.parse(localStorage.getItem('jam_kerja_bidang_list') || '[]');
    const today = getTodayName();
    const jamKerjaBidang = jamKerjaBidangList.find((j: any) => j.hari === today);

    if (!jamKerjaBidang || !jamKerjaBidang.jam_kerja_id) {
      return {
        valid: false,
        error: 'Tidak ada jadwal jam kerja untuk hari ini.',
        info: ''
      };
    }

    const jamKerja = jamKerjaList.find((j: any) => j.id == jamKerjaBidang.jam_kerja_id);
    if (!jamKerja) {
      return {
        valid: false,
        error: 'Data jam kerja tidak ditemukan.',
        info: ''
      };
    }

    const now = new Date();
    const pad = (n: number) => n.toString().padStart(2, '0');
    const nowStr = pad(now.getHours()) + ':' + pad(now.getMinutes());
    const info = `Jam kerja: ${jamKerja.awal_jam_masuk} - ${jamKerja.akhir_jam_pulang}`;

    if (jenisAbsensi === 'masuk') {
      // Validasi untuk absen masuk
      if (!jamKerja.awal_jam_masuk || !jamKerja.akhir_jam_masuk) {
        return {
          valid: false,
          error: 'Data jam masuk tidak lengkap.',
          info: info
        };
      }

      if (nowStr < jamKerja.awal_jam_masuk) {
        return {
          valid: false,
          error: `Absen masuk belum bisa dilakukan. Waktu absen masuk: ${jamKerja.awal_jam_masuk} - ${jamKerja.akhir_jam_masuk}`,
          info: info
        };
      }

      if (nowStr > jamKerja.akhir_jam_masuk) {
        return {
          valid: false,
          error: `Waktu absen masuk sudah berakhir. Waktu absen masuk: ${jamKerja.awal_jam_masuk} - ${jamKerja.akhir_jam_masuk}`,
          info: info
        };
      }

    } else {
      // Validasi untuk absen pulang
      if (!jamKerja.jam_pulang || !jamKerja.akhir_jam_pulang) {
        return {
          valid: false,
          error: 'Data jam pulang tidak lengkap.',
          info: info
        };
      }

      if (nowStr < jamKerja.jam_pulang) {
        return {
          valid: false,
          error: `Absen pulang belum bisa dilakukan. Waktu absen pulang: ${jamKerja.jam_pulang} - ${jamKerja.akhir_jam_pulang}`,
          info: info
        };
      }

      if (nowStr > jamKerja.akhir_jam_pulang) {
        return {
          valid: false,
          error: `Waktu absen pulang sudah berakhir. Waktu absen pulang: ${jamKerja.jam_pulang} - ${jamKerja.akhir_jam_pulang}`,
          info: info
        };
      }
    }

    return {
      valid: true,
      error: '',
      info: info
    };
  };

  // Fungsi untuk mendapatkan informasi waktu absen
  const getWaktuAbsen = (jenis: 'masuk' | 'pulang') => {
    const jamKerjaList = JSON.parse(localStorage.getItem('jam_kerja_list') || '[]');
    const jamKerjaBidangList = JSON.parse(localStorage.getItem('jam_kerja_bidang_list') || '[]');
    const today = getTodayName();
    const jamKerjaBidang = jamKerjaBidangList.find((j: any) => j.hari === today);

    if (!jamKerjaBidang || !jamKerjaBidang.jam_kerja_id) {
      return 'Data tidak tersedia';
    }

    const jamKerja = jamKerjaList.find((j: any) => j.id == jamKerjaBidang.jam_kerja_id);
    if (!jamKerja) {
      return 'Data tidak tersedia';
    }

    if (jenis === 'masuk') {
      return `${jamKerja.awal_jam_masuk || '--:--'} - ${jamKerja.akhir_jam_masuk || '--:--'}`;
    } else {
      return `${jamKerja.jam_pulang || '--:--'} - ${jamKerja.akhir_jam_pulang || '--:--'}`;
    }
  };

  // Fungsi untuk menentukan status absensi berdasarkan waktu
  const getStatusAbsensi = (jenisAbsensi: 'masuk' | 'pulang', jamSekarang: string) => {
    const jamKerjaList = JSON.parse(localStorage.getItem('jam_kerja_list') || '[]');
    const jamKerjaBidangList = JSON.parse(localStorage.getItem('jam_kerja_bidang_list') || '[]');
    const today = getTodayName();
    const jamKerjaBidang = jamKerjaBidangList.find((j: any) => j.hari === today);

    if (!jamKerjaBidang || !jamKerjaBidang.jam_kerja_id) {
      return 'Tepat Waktu'; // Default jika tidak ada data
    }

    const jamKerja = jamKerjaList.find((j: any) => j.id == jamKerjaBidang.jam_kerja_id);
    if (!jamKerja) {
      return 'Tepat Waktu'; // Default jika tidak ada data
    }

    if (jenisAbsensi === 'masuk') {
      // Logika untuk absen masuk: jika setelah jam_masuk maka terlambat
      const jamMasuk = jamKerja.jam_masuk; // Jam masuk normal

      if (jamSekarang <= jamMasuk) {
        return 'Tepat Waktu';
      } else {
        return 'Terlambat';
      }
    } else {
      // Logika untuk absen pulang: jika sebelum jam_pulang maka pulang awal
      const jamPulang = jamKerja.jam_pulang; // Jam pulang normal

      if (jamSekarang < jamPulang) {
        return 'Pulang Awal';
      } else {
        return 'Tepat Waktu';
      }
    }
  };

  // Validasi jam kerja saat halaman dibuka dan saat jenis absensi berubah
  useEffect(() => {
    setError('');
    setJamKerjaValid(false);
    setJamKerjaInfo('');

    const validation = validateJamKerja(jenisAbsensi);
    setJamKerjaInfo(validation.info);

    if (!validation.valid) {
      setError(validation.error);
      setJamKerjaValid(false);
      return;
    }

    setJamKerjaValid(true);
    startCamera();
    // eslint-disable-next-line
  }, [jenisAbsensi]);

  // Update waktu real-time
  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      const pad = (n: number) => n.toString().padStart(2, '0');
      const timeStr = pad(now.getHours()) + ':' + pad(now.getMinutes()) + ':' + pad(now.getSeconds());
      setCurrentTime(timeStr);
    };

    updateTime(); // Set initial time
    const interval = setInterval(updateTime, 1000);

    return () => clearInterval(interval);
  }, []);



  const handleValidasi = async () => {
    setError('');

    // Validasi ulang jam kerja saat tombol ditekan (untuk memastikan waktu masih valid)
    const validation = validateJamKerja(jenisAbsensi);
    if (!validation.valid) {
      setError(validation.error);
      return;
    }

    if (!lokasiValid) {
      setError('Lokasi tidak valid. Pastikan Anda berada dalam radius kantor.');
      return;
    }

    if (!photo) {
      setError('Foto wajah diperlukan untuk absensi.');
      return;
    }

    setSubmitting(true); // Mulai loading

    // Deklarasi payload di scope yang lebih luas
    let payload: any;

    try {
      // Ambil lokasi terkini untuk dikirim ke API
      const currentLocation = await getLocation();

      // Siapkan data untuk dikirim ke API
      const now = new Date();
      const tanggal = now.toISOString().split('T')[0]; // Format: YYYY-MM-DD
      const jam = now.toTimeString().split(' ')[0]; // Format: HH:MM:SS
      const lokasi = `${currentLocation.lat},${currentLocation.lng}`;

      // Tentukan status berdasarkan waktu absensi
      const statusAbsensi = getStatusAbsensi(jenisAbsensi, jam.substring(0, 5)); // Ambil HH:MM saja

      payload = {
        api_key: 'absensiku_api_key_2023',
        user_id: user.id || user.nik, // Gunakan ID user atau NIK sebagai fallback
        tanggal: tanggal,
        status: statusAbsensi, // Gunakan status yang sudah dihitung
        keterangan: `Absensi ${jenisAbsensi} melalui aplikasi mobile - Status: ${statusAbsensi}`,
        jenisAbsensi: jenisAbsensi // Tambahkan info jenis absensi untuk offline storage
      };

      if (jenisAbsensi === 'masuk') {
        payload.jam_masuk = jam;
        payload.foto_masuk_base64 = photo;
        payload.lokasi_masuk = lokasi;
      } else {
        payload.jam_pulang = jam;
        payload.foto_pulang_base64 = photo;
        payload.lokasi_pulang = lokasi;
      }

      // Cek status koneksi
      if (!navigator.onLine) {
        // Jika offline, simpan ke localStorage
        const savedData = saveAbsensiToLocalStorage(payload);
        if (savedData) {
          present({
            message: `Absensi ${jenisAbsensi} disimpan offline. Data akan dikirim otomatis saat online.`,
            color: 'warning',
            duration: 4000,
            position: 'top'
          });

          // Reset form
          setPhoto(null);
          stopCamera();

          // Redirect setelah delay
          setTimeout(() => {
            window.location.href = '/home';
          }, 2000);

          return; // Keluar dari fungsi
        } else {
          throw new Error('Gagal menyimpan data offline');
        }
      }

      // Kirim data ke API
      let response;
      if (jenisAbsensi === 'masuk') {
        // Untuk absensi masuk, gunakan POST
        response = await fetch('https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload)
        });
      } else {
        // Untuk absensi pulang, cari ID record hari ini dulu
        const today = new Date().toISOString().split('T')[0];
        const checkResponse = await fetch(`https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023&user_id=${user.id || user.nik}&tanggal=${today}`);
        const checkResult = await checkResponse.json();

        if (checkResult.status === 'success' && checkResult.data.length > 0) {
          const recordId = checkResult.data[0].id;
          payload.id = recordId;

          // Gunakan PUT untuk update record yang sudah ada
          response = await fetch('https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload)
          });
        } else {
          throw new Error('Data absensi masuk tidak ditemukan untuk hari ini');
        }
      }

      const result = await response.json();

      if (result.status === 'success') {
        // Jika berhasil kirim ke server, simpan juga sebagai backup di localStorage
        const backupData = { ...payload, synced: true, timestamp: new Date().toISOString() };
        const existingBackup = JSON.parse(localStorage.getItem('absensi_backup') || '[]');
        existingBackup.push(backupData);
        // Simpan maksimal 30 hari terakhir
        if (existingBackup.length > 30) {
          existingBackup.splice(0, existingBackup.length - 30);
        }
        localStorage.setItem('absensi_backup', JSON.stringify(existingBackup));

        present({
          message: `Absensi ${jenisAbsensi} berhasil disimpan!`,
          color: 'success',
          duration: 3000,
          position: 'top'
        });

        // Reset form setelah berhasil
        setPhoto(null);
        stopCamera();

        // Optional: Redirect ke halaman home setelah beberapa detik
        setTimeout(() => {
          window.location.href = '/home';
        }, 2000);

      } else {
        // Jika gagal kirim ke server, simpan ke offline queue
        const savedData = saveAbsensiToLocalStorage(payload);
        if (savedData) {
          present({
            message: `Server error: ${result.message}. Data disimpan offline dan akan dikirim ulang otomatis.`,
            color: 'warning',
            duration: 4000,
            position: 'top'
          });

          // Reset form
          setPhoto(null);
          stopCamera();

          setTimeout(() => {
            window.location.href = '/home';
          }, 2000);
        } else {
          setError(result.message || 'Gagal menyimpan data absensi');
          present({
            message: result.message || 'Gagal menyimpan data absensi',
            color: 'danger',
            duration: 3000,
            position: 'top'
          });
        }
      }

    } catch (error) {
      console.error('Error saat mengirim data absensi:', error);

      // Jika terjadi error (network, dll), simpan ke offline storage
      const savedData = saveAbsensiToLocalStorage(payload);
      if (savedData) {
        present({
          message: `Koneksi bermasalah. Absensi ${jenisAbsensi} disimpan offline dan akan dikirim otomatis saat online.`,
          color: 'warning',
          duration: 4000,
          position: 'top'
        });

        // Reset form
        setPhoto(null);
        stopCamera();

        setTimeout(() => {
          window.location.href = '/home';
        }, 2000);
      } else {
        setError('Terjadi kesalahan saat menyimpan data. Silakan coba lagi.');
        present({
          message: 'Terjadi kesalahan saat menyimpan data',
          color: 'danger',
          duration: 3000,
          position: 'top'
        });
      }
    } finally {
      setSubmitting(false); // Selesai loading
    }
  };

  useEffect(() => {
    return () => stopCamera();
    // eslint-disable-next-line
  }, []);

  return (
    <IonPage>
      <IonHeader style={headerStyle}>
        <IonToolbar color="transparent" style={{ background: 'transparent', minHeight: 80, boxShadow: 'none' }}>
          <IonButtons slot="start">
            <IonBackButton defaultHref="/home" text="" style={{ color: '#fff', fontSize: 28, marginLeft: 4, background: 'rgba(0, 0, 0, 0)', borderRadius: 12, padding: 4 }} />
          </IonButtons>
          <IonTitle style={titleStyle}>Absensi {jenisAbsensi === 'masuk' ? 'Masuk' : 'Pulang'}</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent className="ion-padding" fullscreen>
        <div style={{ maxWidth: 400, margin: '0 auto', textAlign: 'center' }}>

          {/* Status Absensi - Paling Atas */}
          <div style={{ margin: '16px 0 24px 0', padding: '16px', borderRadius: '16px', backgroundColor: jenisAbsensi === 'masuk' ? '#e8f5e8' : '#fff3cd', border: `2px solid ${jenisAbsensi === 'masuk' ? '#4caf50' : '#ff9800'}` }}>
            <IonText color={jenisAbsensi === 'masuk' ? 'success' : 'warning'}>
              <h2 style={{ margin: '0', fontSize: '1.5rem', fontWeight: 'bold' }}>
                {jenisAbsensi === 'masuk' ? '📥 Absensi Masuk' : '📤 Absensi Pulang'}
              </h2>
            </IonText>
          </div>

          {/* Tombol Toggle untuk Show/Hide Details */}
          <IonButton
            fill="outline"
            size="small"
            color="medium"
            onClick={() => setShowDetails(!showDetails)}
            style={{
              margin: '0 0 16px 0',
              '--border-radius': '20px',
              '--padding-start': '16px',
              '--padding-end': '16px'
            }}
          >
            <IonIcon
              icon={showDetails ? chevronUpOutline : chevronDownOutline}
              slot="start"
            />
            {showDetails ? 'Sembunyikan Detail' : 'Tampilkan Detail'}
          </IonButton>

          {/* Detail Information - Dapat di-hide/show */}
          {showDetails && (
            <div style={{ marginBottom: '20px' }}>
              {/* Nama Karyawan */}
              <div style={{ margin: '12px 0', padding: '12px', borderRadius: '12px', backgroundColor: '#f0f8ff', border: '1px solid #e1f5fe' }}>
                <IonText color="primary">
                  <p style={{ fontSize: '1.1rem', fontWeight: 'bold', margin: '0' }}>
                    👤 {user.nama || 'Nama Karyawan'}
                  </p>
                </IonText>
              </div>

              {/* Waktu Sekarang */}
              <div style={{ margin: '12px 0', padding: '12px', borderRadius: '12px', backgroundColor: '#f8f9fa', border: '1px solid #e9ecef' }}>
                <IonText color="dark">
                  <p style={{ fontSize: '1.1rem', fontWeight: 'bold', margin: '0' }}>
                    🕐 Waktu Sekarang: {currentTime}
                  </p>
                </IonText>
              </div>

              {/* Jam Kerja */}
              {jamKerjaInfo && (
                <div style={{ margin: '12px 0', padding: '12px', borderRadius: '12px', backgroundColor: '#1880ff', border: '1px solid #1880ff' }}>
                  <IonText color="light">
                    <p style={{ fontSize: '0.95rem', margin: '0' }}>
                      📋 {jamKerjaInfo}
                    </p>
                  </IonText>
                </div>
              )}

              {/* Waktu Absen Spesifik */}
              {jamKerjaValid && (
                <div style={{ margin: '12px 0', padding: '12px', borderRadius: '12px', backgroundColor: '#e3f2fd', border: '1px solid #bbdefb' }}>
                  <IonText color="primary">
                    <p style={{ fontSize: '0.9rem', margin: '0' }}>
                      {jenisAbsensi === 'masuk'
                        ? `⏰ Waktu absen masuk: ${getWaktuAbsen('masuk')}`
                        : `⏰ Waktu absen pulang: ${getWaktuAbsen('pulang')}`
                      }
                    </p>
                  </IonText>
                </div>
              )}

              {/* Status Koneksi */}
              <div style={{ margin: '12px 0', padding: '8px', borderRadius: '8px', backgroundColor: isOnline ? '#e8f5e8' : '#fff3cd' }}>
                <IonText color={isOnline ? 'success' : 'warning'}>
                  <small>
                    {isOnline ? '🟢 Online' : '🔴 Offline'}
                    {offlineCount > 0 && ` • ${offlineCount} data menunggu sinkronisasi`}
                  </small>
                </IonText>
              </div>
            </div>
          )}

          {/* Tampilkan status validasi lokasi tanpa peta */}
          {lokasiStatus && <IonText color={lokasiStatus.includes('valid') ? 'success' : 'danger'}><p>{lokasiStatus}</p></IonText>}
          {showRefresh && (
            <IonButton color="tertiary" onClick={handleRefreshLokasi} style={{ margin: '12px 0' }}>
              <IonIcon icon={locationOutline} slot="start" /> Refresh Lokasi
            </IonButton>
          )}
          <div style={{ margin: '18px 0' }}>
            {!photo ? (
              <video ref={videoRef} autoPlay playsInline style={{ width: '100%', borderRadius: 18, background: '#000' }} />
            ) : (
              <img src={photo} alt="Foto Wajah" style={{ width: '100%', borderRadius: 18, objectFit: 'cover' }} />
            )}
            <canvas ref={canvasRef} style={{ display: 'none' }} />
          </div>
          {error && <IonText color="danger"><p>{error}</p></IonText>}
          <div style={{ display: 'flex', justifyContent: 'center', gap: 12, margin: '18px 0' }}>
            {!photo ? (
              <IonButton color="primary" onClick={takePhoto} size="large" shape="round" disabled={!jamKerjaValid || !lokasiValid}>
                <IonIcon icon={cameraOutline} slot="start" /> Ambil Foto
              </IonButton>
            ) : (
              <IonButton color="medium" onClick={retakePhoto} size="large" shape="round">
                <IonIcon icon={refreshOutline} slot="start" /> Ulangi
              </IonButton>
            )}
            {photo && (
              <IonButton
                color="success"
                onClick={handleValidasi}
                size="large"
                shape="round"
                disabled={submitting}
              >
                <IonIcon icon={checkmarkCircleOutline} slot="start" />
                {submitting ? 'Mengirim...' : `Absen ${jenisAbsensi === 'masuk' ? 'Masuk' : 'Pulang'}`}
              </IonButton>
            )}
          </div>
        </div>
        <IonLoading
          isOpen={submitting}
          message="Mengirim data absensi..."
          duration={0}
        />
      </IonContent>
    </IonPage>
  );
};

export default Absensi; 