export async function fetchAndStoreJamKerjaBidang() {
  try {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const bidangId = user.bidang_id;
    if (!bidangId) return;
    // Ambil id jam kerja user dari localStorage (jam_kerja_list)
    const jamKerjaList = JSON.parse(localStorage.getItem('jam_kerja_list') || '[]');
    const jamKerjaIds = jamKerjaList.map((j: any) => j.id);
    const res = await fetch('https://absensiku.trunois.my.id/api/jam_kerja_bidang.php?api_key=absensiku_api_key_2023');
    const data = await res.json();
    if (data.status === 'success' && Array.isArray(data.data)) {
      const filtered = data.data.filter((item: any) => item.bidang_id == bidangId && jamKerjaIds.includes(item.jam_kerja_id));
      localStorage.setItem('jam_kerja_bidang_list', JSON.stringify(filtered));
    }
  } catch (err) {
    // Optional: handle error (misal: log)
  }
} 