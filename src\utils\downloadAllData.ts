import { fetchAndStoreHariLibur } from './hariLibur';
import { fetchAndStoreJamKerja } from './jamKerja';
import { fetchAndStoreJamKerjaBidang } from './jamKerjaBidang';
import { fetchAndStoreBidang } from './bidang';
import { fetchAndStoreLokasi } from './lokasi';

/**
 * Memanggil semua fungsi fetchAndStore untuk mendownload dan menyimpan data ke localStorage.
 * Mengembalikan status sukses/gagal untuk masing-masing data.
 */
export async function downloadAllDataWithStatus() {
  const results = {
    hariLibur: false,
    jamKerja: false,
    jamKerjaBidang: false,
    bidang: false,
    lokasi: false,
  };
  try {
    await fetchAndStoreHariLibur();
    results.hariLibur = !!localStorage.getItem('hari_libur_list');
  } catch { results.hariLibur = false; }
  try {
    await fetchAndStoreJamKerja();
    results.jamKerja = !!localStorage.getItem('jam_kerja_list');
  } catch { results.jamKerja = false; }
  try {
    await fetchAndStoreJamKerjaBidang();
    results.jamKerjaBidang = !!localStorage.getItem('jam_kerja_bidang_list');
  } catch { results.jamKerjaBidang = false; }
  try {
    await fetchAndStoreBidang();
    results.bidang = !!localStorage.getItem('bidang_list');
  } catch { results.bidang = false; }
  try {
    await fetchAndStoreLokasi();
    results.lokasi = !!localStorage.getItem('lokasi_list');
  } catch { results.lokasi = false; }
  return results;
} 