export interface RapatData {
  id: string;
  judul: string;
  tanggal: string;
  waktu_mulai: string;
  waktu_selesai: string;
  lokasi: string;
  deskripsi: string;
  barcode_value: string;
  created_by: string;
  penanggung_jawab_id: string;
  created_at: string;
  updated_at: string;
}

export interface RapatPesertaData {
  id: string;
  rapat_id: string;
  user_id: string;
  status: string | null;
  waktu_hadir: string | null;
  created_at: string;
}

export interface ApiResponse<T> {
  status: string;
  data: T;
  message?: string;
}

const API_BASE_URL = 'https://absensiku.trunois.my.id/api';
const API_KEY = 'absensiku_api_key_2023';

/**
 * Service untuk mengelola API rapat
 */
export class RapatService {
  
  /**
   * Mengambil semua data rapat
   */
  static async getAllRapat(): Promise<RapatData[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/api_rapat.php?api_key=${API_KEY}`);
      const result: ApiResponse<RapatData[]> = await response.json();
      
      if (result.status === 'success' && Array.isArray(result.data)) {
        return result.data;
      } else {
        console.error('Failed to fetch rapat data:', result);
        return [];
      }
    } catch (error) {
      console.error('Error fetching rapat data:', error);
      return [];
    }
  }

  /**
   * Mengambil data rapat berdasarkan ID
   */
  static async getRapatById(id: string): Promise<RapatData | null> {
    try {
      const response = await fetch(`${API_BASE_URL}/api_rapat.php?api_key=${API_KEY}&id=${id}`);
      const result: ApiResponse<RapatData[]> = await response.json();
      
      if (result.status === 'success' && Array.isArray(result.data) && result.data.length > 0) {
        return result.data[0];
      } else {
        return null;
      }
    } catch (error) {
      console.error('Error fetching rapat by ID:', error);
      return null;
    }
  }

  /**
   * Mengambil semua data peserta rapat
   */
  static async getAllRapatPeserta(): Promise<RapatPesertaData[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/api_rapat_peserta.php?api_key=${API_KEY}`);
      const result: ApiResponse<RapatPesertaData[]> = await response.json();
      
      if (result.status === 'success' && Array.isArray(result.data)) {
        return result.data;
      } else {
        console.error('Failed to fetch rapat peserta data:', result);
        return [];
      }
    } catch (error) {
      console.error('Error fetching rapat peserta data:', error);
      return [];
    }
  }

  /**
   * Mengambil data peserta rapat berdasarkan user ID
   */
  static async getRapatPesertaByUserId(userId: string): Promise<RapatPesertaData[]> {
    try {
      console.log('Fetching rapat peserta for user:', userId);

      // Ambil semua data peserta dulu, lalu filter di frontend
      // Karena API backend mungkin tidak support filter user_id
      const response = await fetch(`${API_BASE_URL}/api_rapat_peserta.php?api_key=${API_KEY}`);
      const result: ApiResponse<RapatPesertaData[]> = await response.json();

      console.log('All rapat peserta response:', result);

      if (result.status === 'success' && Array.isArray(result.data)) {
        // Filter data berdasarkan user_id di frontend
        const filteredData = result.data.filter(p => p.user_id == userId);
        console.log('Filtered peserta data for user', userId, ':', filteredData);
        return filteredData;
      } else {
        console.error('Failed to fetch rapat peserta:', result);
        return [];
      }
    } catch (error) {
      console.error('Error fetching rapat peserta:', error);
      return [];
    }
  }

  /**
   * Mengambil data peserta rapat berdasarkan rapat ID
   */
  static async getRapatPesertaByRapatId(rapatId: string): Promise<RapatPesertaData[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/api_rapat_peserta.php?api_key=${API_KEY}&rapat_id=${rapatId}`);
      const result: ApiResponse<RapatPesertaData[]> = await response.json();
      
      if (result.status === 'success' && Array.isArray(result.data)) {
        return result.data;
      } else {
        console.error('Failed to fetch rapat peserta by rapat ID:', result);
        return [];
      }
    } catch (error) {
      console.error('Error fetching rapat peserta by rapat ID:', error);
      return [];
    }
  }

  /**
   * Update status kehadiran peserta rapat - Menggunakan rapat_id dan user_id
   */
  static async updateStatusKehadiran(
    rapatId: string,
    userId: string,
    status: 'hadir' | 'tidak_hadir',
    waktuHadir?: string
  ): Promise<boolean> {
    try {
      const waktu = waktuHadir || new Date().toISOString().slice(0, 19).replace('T', ' ');

      console.log('=== UPDATE STATUS KEHADIRAN ===');
      console.log('Rapat ID:', rapatId);
      console.log('User ID:', userId);
      console.log('Status:', status);
      console.log('Waktu:', waktu);

      // Sesuai dengan API backend yang menggunakan rapat_id dan user_id
      const payload = {
        api_key: API_KEY,
        rapat_id: rapatId,
        user_id: userId,
        status: status,
        waktu_hadir: waktu
      };

      console.log('Payload:', payload);

      // Gunakan PUT sesuai dengan API backend
      const response = await fetch(`${API_BASE_URL}/api_rapat_peserta.php`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      console.log('Response status:', response.status);
      console.log('Response ok:', response.ok);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('HTTP error response:', errorText);
        return false;
      }

      // Ambil response sebagai text dulu untuk debug
      const responseText = await response.text();
      console.log('Raw response:', responseText);

      // Coba parse JSON
      try {
        const result: ApiResponse<any> = JSON.parse(responseText);
        console.log('Parsed result:', result);

        if (result.status === 'success') {
          console.log('Update successful!');

          // Clear cache setelah update berhasil
          localStorage.removeItem('rapat_peserta_cache');

          return true;
        } else if (result.status === 'warning') {
          console.log('Update warning (no changes):', result.message);
          return true; // Anggap berhasil meskipun tidak ada perubahan
        } else {
          console.error('API returned error:', result.message || result);
          return false;
        }
      } catch (jsonError) {
        console.error('JSON parse error:', jsonError);
        console.error('Response was:', responseText);
        return false;
      }

    } catch (error) {
      console.error('Error updating status kehadiran:', error);
      return false;
    }
  }

  /**
   * Mencari rapat berdasarkan barcode value
   */
  static async getRapatByBarcode(barcodeValue: string): Promise<RapatData | null> {
    try {
      const allRapat = await this.getAllRapat();
      const rapat = allRapat.find(r => r.barcode_value === barcodeValue);
      return rapat || null;
    } catch (error) {
      console.error('Error finding rapat by barcode:', error);
      return null;
    }
  }

  /**
   * Cek apakah user terdaftar sebagai peserta rapat
   */
  static async isUserRegisteredForRapat(userId: string, rapatId: string): Promise<RapatPesertaData | null> {
    try {
      const pesertaList = await this.getRapatPesertaByRapatId(rapatId);
      console.log('Checking user registration for user:', userId, 'in rapat:', rapatId);
      console.log('Available peserta:', pesertaList.map(p => ({ id: p.id, user_id: p.user_id, rapat_id: p.rapat_id })));

      const peserta = pesertaList.find(p => p.user_id == userId); // Gunakan == untuk type coercion
      console.log('Found peserta:', peserta);

      return peserta || null;
    } catch (error) {
      console.error('Error checking user registration:', error);
      return null;
    }
  }

  /**
   * Proses absensi rapat melalui barcode
   */
  static async processRapatAbsensi(barcodeValue: string, userId: string): Promise<{
    success: boolean;
    message: string;
    rapat?: RapatData;
  }> {
    try {
      console.log('=== PROCESSING RAPAT ABSENSI ===');
      console.log('Barcode:', barcodeValue);
      console.log('User ID:', userId);

      // 1. Cari rapat berdasarkan barcode
      const rapat = await this.getRapatByBarcode(barcodeValue);
      console.log('Found rapat:', rapat);

      if (!rapat) {
        return {
          success: false,
          message: 'Barcode rapat tidak ditemukan'
        };
      }

      // 2. Cek apakah user terdaftar sebagai peserta
      const peserta = await this.isUserRegisteredForRapat(userId, rapat.id);
      console.log('Found peserta:', peserta);

      if (!peserta) {
        return {
          success: false,
          message: 'Anda tidak terdaftar sebagai peserta rapat ini'
        };
      }

      // 3. Cek apakah sudah absen
      if (peserta.status === 'hadir') {
        return {
          success: false,
          message: 'Anda sudah melakukan absensi untuk rapat ini'
        };
      }

      // 4. Update status kehadiran
      console.log('Updating status kehadiran for rapat:', rapat.id, 'user:', userId);
      const updateSuccess = await this.updateStatusKehadiran(rapat.id, userId, 'hadir');
      console.log('Update result:', updateSuccess);

      if (updateSuccess) {
        return {
          success: true,
          message: `Berhasil absen untuk rapat: ${rapat.judul}`,
          rapat: rapat
        };
      } else {
        return {
          success: false,
          message: 'Gagal mengupdate status kehadiran. Silakan coba lagi.'
        };
      }
    } catch (error) {
      console.error('Error processing rapat absensi:', error);
      return {
        success: false,
        message: 'Terjadi kesalahan saat memproses absensi: ' + error
      };
    }
  }

  /**
   * Format tanggal untuk tampilan
   */
  static formatTanggal(tanggal: string): string {
    const date = new Date(tanggal);
    return date.toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  /**
   * Format waktu untuk tampilan
   */
  static formatWaktu(waktu: string): string {
    return waktu.substring(0, 5); // Ambil HH:MM saja
  }

  /**
   * Cek apakah rapat sedang berlangsung
   */
  static isRapatActive(rapat: RapatData): boolean {
    const now = new Date();
    const rapatDate = new Date(rapat.tanggal);
    const [startHour, startMinute] = rapat.waktu_mulai.split(':').map(Number);
    const [endHour, endMinute] = rapat.waktu_selesai.split(':').map(Number);
    
    const startTime = new Date(rapatDate);
    startTime.setHours(startHour, startMinute, 0, 0);
    
    const endTime = new Date(rapatDate);
    endTime.setHours(endHour, endMinute, 0, 0);
    
    return now >= startTime && now <= endTime;
  }

  /**
   * Simpan data rapat ke localStorage untuk offline access
   */
  static async saveRapatToLocalStorage(): Promise<void> {
    try {
      const rapatData = await this.getAllRapat();
      const rapatPesertaData = await this.getAllRapatPeserta();
      
      localStorage.setItem('rapat_list', JSON.stringify(rapatData));
      localStorage.setItem('rapat_peserta_list', JSON.stringify(rapatPesertaData));
      localStorage.setItem('rapat_last_sync', new Date().toISOString());
    } catch (error) {
      console.error('Error saving rapat data to localStorage:', error);
    }
  }

  /**
   * Ambil data rapat dari localStorage
   */
  static getRapatFromLocalStorage(): {
    rapatList: RapatData[];
    rapatPesertaList: RapatPesertaData[];
    lastSync: string | null;
  } {
    try {
      const rapatList = JSON.parse(localStorage.getItem('rapat_list') || '[]');
      const rapatPesertaList = JSON.parse(localStorage.getItem('rapat_peserta_list') || '[]');
      const lastSync = localStorage.getItem('rapat_last_sync');
      
      return { rapatList, rapatPesertaList, lastSync };
    } catch (error) {
      console.error('Error getting rapat data from localStorage:', error);
      return { rapatList: [], rapatPesertaList: [], lastSync: null };
    }
  }
}
