export async function fetchAndStoreLokasi() {
  try {
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const lokasiId = user.lokasi_id;
    if (!lokasiId) return;
    const res = await fetch('https://absensiku.trunois.my.id/api/lokasi.php?api_key=absensiku_api_key_2023');
    const data = await res.json();
    if (data.status === 'success' && Array.isArray(data.data)) {
      const lokasi = data.data.filter((l: any) => l.id == lokasiId);
      localStorage.setItem('lokasi_list', JSON.stringify(lokasi));
    }
  } catch (err) {
    // Optional: handle error (misal: log)
  }
} 