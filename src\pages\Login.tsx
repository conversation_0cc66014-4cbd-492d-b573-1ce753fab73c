import React, { useState } from 'react';
import {
  IonPage, IonContent, IonInput, IonItem, IonLabel, IonButton, IonToast, IonLoading, IonCard, IonCardHeader, IonCardTitle, IonCardContent, IonIcon, IonText, IonList
} from '@ionic/react';
import { personCircleOutline, lockClosedOutline, logInOutline, eyeOutline, eyeOffOutline } from 'ionicons/icons';
import { useHistory } from 'react-router-dom';
import './Login.css';
import { v4 as uuidv4 } from 'uuid';

const Login: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const history = useHistory();

  const getDeviceId = () => {
    let deviceId = localStorage.getItem('device_id');
    if (!deviceId) {
      deviceId = uuidv4();
      localStorage.setItem('device_id', deviceId);
    }
    return deviceId;
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    try {
      const deviceId = getDeviceId();
      // 1. Cek status akun user
      const statusUrl = `https://absensiku.trunois.my.id/api/status_user.php?api_key=absensiku_api_key_2023&nik=${encodeURIComponent(username)}&device_id=${encodeURIComponent(deviceId)}`;
      const statusRes = await fetch(statusUrl);
      const statusJson = await statusRes.json();
      if (statusJson.status === 'success') {
        const statusData = statusJson.data;
        if (Array.isArray(statusData) && statusData.length > 0) {
          const userStatus = statusData[0];
          if (userStatus.status_akun === 'blokir') {
            setToastMessage('Akun Anda diblokir, silakan hubungi admin');
            setShowToast(true);
            setLoading(false);
            return;
          }
          if (userStatus.status_login === 'login') {
            // Jika device_id sama, izinkan login
            if (userStatus.device_id && userStatus.device_id === deviceId) {
              // lanjutkan login
            } else {
              setToastMessage('Akun sudah login di perangkat lain, tidak bisa login lagi');
              setShowToast(true);
              setLoading(false);
              return;
            }
          }
        }
        // Jika data kosong atau status_login = 'tidak' dan status_akun = 'aktif', lanjutkan login
      } else {
        setToastMessage('Gagal cek status akun');
        setShowToast(true);
        setLoading(false);
        return;
      }

      // 2. Proses login seperti biasa
      const url = `https://absensiku.trunois.my.id/api/karyawan.php?api_key=absensiku_api_key_2023&nik=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`;
      const response = await fetch(url);
      const result = await response.json();
      if (result.status === 'success') {
        // Simpan data user ke localStorage/session jika perlu
        localStorage.setItem('user', JSON.stringify(result.data));

        // Kirim status login ke backend
        try {
          // Cek apakah data status_user sudah ada
          const statusUrl = `https://absensiku.trunois.my.id/api/status_user.php?api_key=absensiku_api_key_2023&nik=${encodeURIComponent(username)}`;
          const statusRes = await fetch(statusUrl);
          const statusJson = await statusRes.json();
          let method = 'POST';
          let bodyData: any = {
            api_key: 'absensiku_api_key_2023',
            nik: username,
            status_login: 'login',
            status_akun: 'aktif',
            device_id: deviceId,
          };
          let url = 'https://absensiku.trunois.my.id/api/status_user.php';
          if (statusJson.status === 'success' && Array.isArray(statusJson.data) && statusJson.data.length > 0) {
            // Sudah ada data, update (PUT)
            method = 'PUT';
            bodyData.id = statusJson.data[0].id;
          }
          await fetch(url, {
            method,
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(bodyData),
          });
        } catch (e) {
          // Optional: bisa tampilkan toast jika gagal update status login
        }

        history.push('/home');
      } else {
        setToastMessage(result.message || 'NIK atau password salah');
        setShowToast(true);
      }
    } catch (error) {
      setToastMessage('Terjadi kesalahan koneksi');
      setShowToast(true);
    } finally {
      setLoading(false);
    }
  };

  return (
    <IonPage>
      <IonContent className="ion-padding login-bg" fullscreen>
        <div className="login-container">
          <img src="/logo192.png" alt="Logo" className="login-logo" />
          <IonText color="primary">
            <h2 className="login-title">Absensi Karyawan</h2>
          </IonText>
          <p className="login-desc">Silakan login untuk melanjutkan</p>
          <IonCard className="login-card">
            <IonCardHeader>
              <IonCardTitle>Login</IonCardTitle>
            </IonCardHeader>
            <IonCardContent>
              <form onSubmit={handleLogin} autoComplete="off">
                <IonList lines="none" className="login-list">
                  <IonItem className="login-input-item">
                    <IonLabel position="stacked">NIK</IonLabel>
                    <IonIcon icon={personCircleOutline} slot="start" className="login-input-icon" />
                    <IonInput value={username} onIonChange={e => setUsername(e.detail.value!)} required inputmode="numeric" />
                  </IonItem>
                  <IonItem className="login-input-item">
                    <IonLabel position="stacked">Password</IonLabel>
                    <IonIcon icon={lockClosedOutline} slot="start" className="login-input-icon" />
                    <IonInput type={showPassword ? 'text' : 'password'} value={password} onIonChange={e => setPassword(e.detail.value!)} required />
                    <IonIcon
                      icon={showPassword ? eyeOffOutline : eyeOutline}
                      slot="end"
                      className="login-eye-icon"
                      onClick={() => setShowPassword(v => !v)}
                      style={{ cursor: 'pointer' }}
                      title={showPassword ? 'Sembunyikan Password' : 'Lihat Password'}
                    />
                  </IonItem>
                </IonList>
                <IonButton expand="block" type="submit" className="ion-margin-top login-btn" color="primary" shape="round">
                  <IonIcon icon={logInOutline} slot="start" />
                  Login
                </IonButton>
              </form>
            </IonCardContent>
          </IonCard>
        </div>
        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          duration={2000}
          color="danger"
        />
        <IonLoading isOpen={loading} message="Memproses..." />
      </IonContent>
    </IonPage>
  );
};

export default Login; 