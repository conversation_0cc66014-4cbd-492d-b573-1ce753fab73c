import React, { useState, useEffect } from 'react';
import {
  IonPage, IonHeader, IonToolbar, IonTitle, IonContent, IonList, IonItem, IonLabel, IonText, IonIcon, IonButton, IonSpinner, IonCard, IonCardContent, IonCardHeader, IonCardTitle, IonBadge, IonButtons, IonBackButton, IonModal, IonImg, IonSelect, IonSelectOption, IonDatetime, IonToast, IonRefresher, IonRefresherContent
} from '@ionic/react';
import {
  calendarOutline, timeOutline, locationOutline, personOutline, checkmarkCircleOutline, closeCircleOutline, warningOutline, eyeOutline, closeOutline, filterOutline
} from 'ionicons/icons';
import './Histori.css';

interface AbsensiData {
  id: string;
  user_id: string;
  tanggal: string;
  jam_masuk: string | null;
  foto_masuk: string | null;
  lokasi_masuk: string | null;
  jam_pulang: string | null;
  foto_pulang: string | null;
  lokasi_pulang: string | null;
  status: string;
  keterangan: string;
  created_at: string;
  updated_at: string;
}

const Histori: React.FC = () => {
  const user = JSON.parse(localStorage.getItem('user') || '{}');
  const [absensiData, setAbsensiData] = useState<AbsensiData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedImage, setSelectedImage] = useState<{url: string, title: string} | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showFilter, setShowFilter] = useState(false);
  // Set default filter ke bulan dan tahun saat ini
  const currentDate = new Date();
  const currentMonth = (currentDate.getMonth() + 1).toString().padStart(2, '0');
  const currentYear = currentDate.getFullYear().toString();

  const [filterBulan, setFilterBulan] = useState(currentMonth);
  const [filterTahun, setFilterTahun] = useState(currentYear);
  const [filterStatus, setFilterStatus] = useState('');
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');

  // Fungsi untuk mengambil data absensi
  const fetchAbsensiData = async () => {
    if (!user.id && !user.nik) {
      setError('Data user tidak ditemukan');
      setLoading(false);
      return;
    }

    setLoading(true);
    setError('');
    
    try {
      const userId = user.id || user.nik;
      const response = await fetch(`https://absensiku.trunois.my.id/api/monitoring_presensi.php?api_key=absensiku_api_key_2023&user_id=${userId}`);
      const result = await response.json();

      if (result.status === 'success' && Array.isArray(result.data)) {
        // Filter data sesuai user yang login
        const userData = result.data.filter((item: AbsensiData) => 
          item.user_id === userId || item.user_id === user.id || item.user_id === user.nik
        );
        setAbsensiData(userData);
      } else {
        setError('Gagal mengambil data absensi');
      }
    } catch (error) {
      console.error('Error fetching absensi data:', error);
      setError('Terjadi kesalahan koneksi');
    } finally {
      setLoading(false);
    }
  };

  // Filter data berdasarkan bulan dan status
  const getFilteredData = () => {
    let filtered = absensiData;

    // Filter berdasarkan bulan dan tahun
    if (filterBulan && filterTahun) {
      filtered = filtered.filter(item => {
        const itemDate = new Date(item.tanggal);
        const itemMonth = (itemDate.getMonth() + 1).toString().padStart(2, '0'); // Bulan 1-12
        const itemYear = itemDate.getFullYear().toString();

        return itemMonth === filterBulan && itemYear === filterTahun;
      });
    }

    // Filter berdasarkan status
    if (filterStatus) {
      filtered = filtered.filter(item => item.status === filterStatus);
    }

    return filtered.sort((a, b) => new Date(b.tanggal).getTime() - new Date(a.tanggal).getTime());
  };

  // Helper function untuk mendapatkan URL foto
  const getFotoUrl = (fotoName: string | null) => {
    if (!fotoName) return null;
    return `https://absensiku.trunois.my.id/uploads/${fotoName}`;
  };

  // Helper function untuk format tanggal
  const formatTanggal = (tanggal: string) => {
    const date = new Date(tanggal);
    const options: Intl.DateTimeFormatOptions = {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };
    return date.toLocaleDateString('id-ID', options);
  };

  // Helper function untuk format jam
  const formatJam = (jam: string | null) => {
    if (!jam) return '-';
    return jam.substring(0, 5); // Ambil HH:MM saja
  };

  // Helper function untuk mendapatkan status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Tepat Waktu':
        return 'success';
      case 'Terlambat':
        return 'danger';
      case 'Pulang Awal':
        return 'warning';
      default:
        return 'medium';
    }
  };

  // Helper function untuk mendapatkan status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Tepat Waktu':
        return checkmarkCircleOutline;
      case 'Terlambat':
        return closeCircleOutline;
      case 'Pulang Awal':
        return warningOutline;
      default:
        return timeOutline;
    }
  };

  // Fungsi untuk menghitung denda keterlambatan
  const hitungDenda = (item: AbsensiData) => {
    let totalDenda = 0;
    const dendaMessages: string[] = [];

    // Denda keterlambatan jika masuk setelah 07:15
    if (item.jam_masuk) {
      const jamMasuk = item.jam_masuk.substring(0, 5); // Ambil HH:MM
      if (jamMasuk > '07:15') {
        totalDenda += 10000;
        dendaMessages.push(`Terlambat masuk (${jamMasuk}): Rp 10.000`);
      }
    }

    // Denda jika tidak absen pulang
    if (item.jam_masuk && !item.jam_pulang) {
      totalDenda += 5000;
      dendaMessages.push('Tidak absen pulang: Rp 5.000');
    }

    return {
      totalDenda,
      dendaMessages,
      hasDenda: totalDenda > 0
    };
  };

  // Helper function untuk format rupiah
  const formatRupiah = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0
    }).format(amount);
  };

  // Fungsi untuk membuka modal gambar
  const openImageModal = (imageUrl: string, title: string) => {
    setSelectedImage({ url: imageUrl, title });
    setIsModalOpen(true);
  };

  // Fungsi untuk menutup modal
  const closeImageModal = () => {
    setIsModalOpen(false);
    setSelectedImage(null);
  };

  // Fungsi untuk refresh data
  const handleRefresh = async (event: CustomEvent) => {
    await fetchAbsensiData();
    event.detail.complete();
  };

  // Fungsi untuk apply filter
  const applyFilter = () => {
    setShowFilter(false);
    setShowToast(true);
    setToastMessage('Filter berhasil diterapkan');
  };

  // Fungsi untuk reset filter ke bulan ini
  const resetFilter = () => {
    const currentDate = new Date();
    const currentMonth = (currentDate.getMonth() + 1).toString().padStart(2, '0');
    const currentYear = currentDate.getFullYear().toString();

    setFilterBulan(currentMonth);
    setFilterTahun(currentYear);
    setFilterStatus('');
    setShowFilter(false);
    setShowToast(true);
    setToastMessage('Filter direset ke bulan ini');
  };

  useEffect(() => {
    fetchAbsensiData();
  }, []);

  const filteredData = getFilteredData();

  // Style untuk header yang selaras dengan halaman lain
  const headerStyle = {
    background: 'linear-gradient(135deg, #1a65eb 0%, #1a65eb 100%)',
    minHeight: 80,
    boxShadow: 'none'
  };

  const titleStyle = {
    color: '#fff',
    fontSize: '1.2rem',
    fontWeight: 'bold',
    textAlign: 'center' as const
  };

  return (
    <IonPage>
      <IonHeader style={headerStyle}>
        <IonToolbar color="transparent" style={{ background: 'transparent', minHeight: 80, boxShadow: 'none' }}>
          <IonButtons slot="start">
            <IonBackButton defaultHref="/home" text="" style={{ color: '#fff', fontSize: 28, marginLeft: 4, background: 'rgba(0, 0, 0, 0)', borderRadius: 12, padding: 4 }} />
          </IonButtons>
          <IonTitle style={titleStyle}>Histori Absensi</IonTitle>
          <IonButtons slot="end">
            <IonButton onClick={() => setShowFilter(true)} style={{ color: '#fff' }}>
              <IonIcon icon={filterOutline} />
            </IonButton>
          </IonButtons>
        </IonToolbar>
      </IonHeader>

      <IonContent>
        <IonRefresher slot="fixed" onIonRefresh={handleRefresh}>
          <IonRefresherContent />
        </IonRefresher>

        {/* Header Info */}
        <div className="histori-header">
          <IonCard>
            <IonCardContent>
              <div className="histori-user-info">
                <IonIcon icon={personOutline} className="histori-user-icon" />
                <div>
                  <h3>{user.nama || 'Nama User'}</h3>
                  <p>{user.jabatan || 'Jabatan'}</p>
                  <p>NIK: {user.nik || 'N/A'}</p>
                </div>
              </div>
            </IonCardContent>
          </IonCard>
        </div>

        {/* Filter Summary */}
        {(filterBulan || filterStatus) && (
          <div className="histori-filter-summary">
            <IonBadge color="primary">
              {filterBulan && filterTahun && (() => {
                const monthNames = [
                  'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
                  'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
                ];
                const monthIndex = parseInt(filterBulan) - 1;
                return `${monthNames[monthIndex]} ${filterTahun}`;
              })()}
              {filterBulan && filterStatus && ' | '}
              {filterStatus && `Status: ${filterStatus}`}
            </IonBadge>
            <IonButton size="small" fill="clear" onClick={resetFilter}>
              Reset
            </IonButton>
          </div>
        )}

        {/* Ringkasan Total Denda */}
        {!loading && !error && (() => {
          const totalDendaKeseluruhan = filteredData.reduce((total, item) => {
            const dendaInfo = hitungDenda(item);
            return total + dendaInfo.totalDenda;
          }, 0);

          const jumlahPelanggaran = filteredData.filter(item => hitungDenda(item).hasDenda).length;

          if (totalDendaKeseluruhan > 0) {
            return (
              <IonCard style={{ marginBottom: '16px', border: '2px solid #f44336' }}>
                <IonCardContent>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    padding: '8px 0'
                  }}>
                    <div>
                      <div style={{
                        fontSize: '1.1rem',
                        fontWeight: 'bold',
                        color: '#d32f2f',
                        marginBottom: '4px',
                        display: 'flex',
                        alignItems: 'center'
                      }}>
                        <IonIcon icon={warningOutline} style={{ marginRight: '8px' }} />
                        Total Denda Periode Ini
                      </div>
                      <div style={{ fontSize: '0.9rem', color: '#666' }}>
                        {jumlahPelanggaran} pelanggaran dari {filteredData.length} hari kerja
                      </div>
                    </div>
                    <div style={{
                      fontSize: '1.3rem',
                      fontWeight: 'bold',
                      color: '#d32f2f',
                      textAlign: 'right'
                    }}>
                      {formatRupiah(totalDendaKeseluruhan)}
                    </div>
                  </div>
                </IonCardContent>
              </IonCard>
            );
          }
          return null;
        })()}

        {/* Loading State */}
        {loading && (
          <div className="histori-loading">
            <IonSpinner name="crescent" />
            <p>Memuat data absensi...</p>
          </div>
        )}

        {/* Error State */}
        {error && !loading && (
          <div className="histori-error">
            <IonCard>
              <IonCardContent>
                <IonText color="danger">
                  <p>{error}</p>
                </IonText>
                <IonButton expand="block" onClick={fetchAbsensiData}>
                  Coba Lagi
                </IonButton>
              </IonCardContent>
            </IonCard>
          </div>
        )}

        {/* Data List */}
        {!loading && !error && (
          <div className="histori-content">
            {filteredData.length === 0 ? (
              <div className="histori-empty">
                <IonCard>
                  <IonCardContent>
                    <IonText color="medium">
                      <p>Tidak ada data absensi ditemukan</p>
                    </IonText>
                  </IonCardContent>
                </IonCard>
              </div>
            ) : (
              <IonList>
                {filteredData.map((item) => (
                  <IonCard key={item.id} className="histori-card">
                    <IonCardHeader>
                      <IonCardTitle className="histori-card-title">
                        <div className="histori-date">
                          <IonIcon icon={calendarOutline} />
                          <span>{formatTanggal(item.tanggal)}</span>
                        </div>
                        <IonBadge color={getStatusColor(item.status)}>
                          <IonIcon icon={getStatusIcon(item.status)} />
                          {item.status}
                        </IonBadge>
                      </IonCardTitle>
                    </IonCardHeader>
                    <IonCardContent>
                      <div className="histori-time-info">
                        <div className="histori-time-item">
                          <IonIcon icon={timeOutline} color="primary" />
                          <div>
                            <strong>Masuk:</strong> {formatJam(item.jam_masuk)}
                            {item.foto_masuk && (
                              <IonButton 
                                size="small" 
                                fill="clear" 
                                onClick={() => openImageModal(
                                  getFotoUrl(item.foto_masuk)!,
                                  `Foto Absen Masuk - ${formatTanggal(item.tanggal)}`
                                )}
                              >
                                <IonIcon icon={eyeOutline} />
                                Lihat Foto
                              </IonButton>
                            )}
                          </div>
                        </div>
                        <div className="histori-time-item">
                          <IonIcon icon={timeOutline} color="secondary" />
                          <div>
                            <strong>Pulang:</strong> {formatJam(item.jam_pulang)}
                            {item.foto_pulang && (
                              <IonButton 
                                size="small" 
                                fill="clear" 
                                onClick={() => openImageModal(
                                  getFotoUrl(item.foto_pulang)!,
                                  `Foto Absen Pulang - ${formatTanggal(item.tanggal)}`
                                )}
                              >
                                <IonIcon icon={eyeOutline} />
                                Lihat Foto
                              </IonButton>
                            )}
                          </div>
                        </div>
                      </div>
                      {item.keterangan && (
                        <div className="histori-keterangan">
                          <strong>Keterangan:</strong> {item.keterangan}
                        </div>
                      )}

                      {/* Tampilkan denda jika ada */}
                      {(() => {
                        const dendaInfo = hitungDenda(item);
                        if (dendaInfo.hasDenda) {
                          return (
                            <div style={{
                              marginTop: '12px',
                              padding: '12px',
                              backgroundColor: '#ffebee',
                              borderRadius: '8px',
                              border: '1px solid #f44336'
                            }}>
                              <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                marginBottom: '8px',
                                color: '#d32f2f',
                                fontWeight: 'bold'
                              }}>
                                <IonIcon icon={warningOutline} style={{ marginRight: '8px' }} />
                                Denda Keterlambatan
                              </div>
                              {dendaInfo.dendaMessages.map((message, index) => (
                                <div key={index} style={{
                                  fontSize: '0.9rem',
                                  color: '#666',
                                  marginBottom: '4px'
                                }}>
                                  • {message}
                                </div>
                              ))}
                              <div style={{
                                marginTop: '8px',
                                padding: '8px',
                                backgroundColor: '#fff',
                                borderRadius: '4px',
                                textAlign: 'center',
                                fontWeight: 'bold',
                                color: '#d32f2f',
                                fontSize: '1.1rem'
                              }}>
                                Total Denda: {formatRupiah(dendaInfo.totalDenda)}
                              </div>
                            </div>
                          );
                        }
                        return null;
                      })()}
                    </IonCardContent>
                  </IonCard>
                ))}
              </IonList>
            )}
          </div>
        )}

        {/* Filter Modal */}
        <IonModal isOpen={showFilter} onDidDismiss={() => setShowFilter(false)}>
          <IonHeader>
            <IonToolbar>
              <IonTitle>Filter Histori</IonTitle>
              <IonButtons slot="end">
                <IonButton onClick={() => setShowFilter(false)}>
                  <IonIcon icon={closeOutline} />
                </IonButton>
              </IonButtons>
            </IonToolbar>
          </IonHeader>
          <IonContent>
            <div className="histori-filter-content">
              <IonItem>
                <IonLabel position="stacked">Bulan & Tahun</IonLabel>
                <IonDatetime
                  value={`${filterTahun}-${filterBulan}`}
                  onIonChange={e => {
                    const value = Array.isArray(e.detail.value) ? e.detail.value[0] : e.detail.value!;
                    if (value) {
                      const date = new Date(value);
                      const month = (date.getMonth() + 1).toString().padStart(2, '0');
                      const year = date.getFullYear().toString();
                      setFilterBulan(month);
                      setFilterTahun(year);
                    }
                  }}
                  presentation="month-year"
                  locale="id-ID"
                />
              </IonItem>
              <IonItem>
                <IonLabel position="stacked">Status</IonLabel>
                <IonSelect value={filterStatus} onIonChange={e => setFilterStatus(e.detail.value)}>
                  <IonSelectOption value="">Semua Status</IonSelectOption>
                  <IonSelectOption value="Tepat Waktu">Tepat Waktu</IonSelectOption>
                  <IonSelectOption value="Terlambat">Terlambat</IonSelectOption>
                  <IonSelectOption value="Pulang Awal">Pulang Awal</IonSelectOption>
                </IonSelect>
              </IonItem>
              <div className="histori-filter-actions">
                <IonButton expand="block" onClick={applyFilter}>
                  Terapkan Filter
                </IonButton>
                <IonButton expand="block" fill="outline" onClick={resetFilter}>
                  Reset Filter
                </IonButton>
              </div>
            </div>
          </IonContent>
        </IonModal>

        {/* Image Modal */}
        <IonModal isOpen={isModalOpen} onDidDismiss={closeImageModal}>
          <IonHeader>
            <IonToolbar>
              <IonTitle>{selectedImage?.title}</IonTitle>
              <IonButtons slot="end">
                <IonButton onClick={closeImageModal}>
                  <IonIcon icon={closeOutline} />
                </IonButton>
              </IonButtons>
            </IonToolbar>
          </IonHeader>
          <IonContent>
            <div className="histori-image-modal">
              {selectedImage && (
                <IonImg src={selectedImage.url} alt={selectedImage.title} />
              )}
            </div>
          </IonContent>
        </IonModal>

        {/* Toast */}
        <IonToast
          isOpen={showToast}
          onDidDismiss={() => setShowToast(false)}
          message={toastMessage}
          duration={2000}
          color="success"
        />
      </IonContent>
    </IonPage>
  );
};

export default Histori; 